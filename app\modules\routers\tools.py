from fastapi import APIRouter
from app.models.tools_model import ToolActionRequest
from app.modules.services.tools import process_tool_action
from app.core.logger import logger


router = APIRouter()

@router.post("/tool-action")
async def handle_tool_action(request: ToolActionRequest):
    logger.info(f"Received tool action request | action_type={request.action_type} | client_id={request.client_id} | documentversion_id={request.documentversion_id}")
    return await process_tool_action(
        action_type=request.action_type,
        client_id=request.client_id,
        documentversion_id=request.documentversion_id,
        parameters=request.parameters
    ) 