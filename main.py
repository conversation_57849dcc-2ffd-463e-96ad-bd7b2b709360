from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import (
    API_TITLE,
    API_VERSION,
    CORS_ORIGINS,
    CORS_CREDENTIALS,
    CORS_METHODS,
    CORS_HEADERS,
    OPENAPI_URL,
    DOCS_URL,
    REDOC_URL
)
from app.modules.router import router as modules_router
from app.core.logger import logger
from app.core.middleware import get_custom_openapi
from app.core.docs import setup_docs_routes
from app.connections.summary_database import init_summary_db, close_summary_db_connection
from app.connections.analytics_database import init_analytics_db, close_analytics_db_connection

from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting FastAPI application")
    # Initialize summary database tables
    await init_summary_db()
    logger.info("Summary database initialized")
    # Initialize analytics database tables
    await init_analytics_db()
    logger.info("Analytics database initialized")
    yield
    # Close summary database connections on shutdown
    await close_analytics_db_connection()
    logger.info("Analytics database connection closed")
    await close_summary_db_connection()
    logger.info("Summary database connection closed")
    logger.info("FastAPI application stopped")

app = FastAPI(
    title=API_TITLE,
    version=API_VERSION,
    openapi_url=OPENAPI_URL,
    docs_url=DOCS_URL,
    redoc_url=REDOC_URL,
    lifespan=lifespan,
)

# Set up custom OpenAPI schema
app.openapi = get_custom_openapi(app)

# Set up custom docs routes
setup_docs_routes(app)

app.include_router(modules_router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=CORS_CREDENTIALS,
    allow_methods=CORS_METHODS,
    allow_headers=CORS_HEADERS,
)


# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8080)

