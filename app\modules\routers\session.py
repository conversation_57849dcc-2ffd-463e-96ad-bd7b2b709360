from fastapi import APIRouter, HTTPException
from app.modules.services.session_service import SessionService
from app.core.logger import logger
from typing import Dict

router = APIRouter()

session_service = SessionService()

@router.post("/cleanup")
async def cleanup_sessions() -> Dict[str, int]:

    try:
        cleaned_count = session_service.cleanup_inactive_sessions()
        logger.info(f"Successfully cleaned up {cleaned_count} inactive sessions")
        return {
            "status": "success",
            "cleaned_sessions": cleaned_count,
            "message": f"Successfully cleaned up {cleaned_count} inactive sessions"
        }

    except Exception as e:
        logger.error(f"Error cleaning up sessions | error={str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clean up sessions: {str(e)}"
        )