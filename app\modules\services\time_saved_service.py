from datetime import datetime, timezone
from typing import Optional
from sqlalchemy.future import select
from sqlalchemy import text
from fastapi import HTTPException
from app.connections.analytics_database import get_analytics_db
from app.models.analytics.time_saved import TimeSaved
from app.modules.routers.summary_analytics import save_summary
from app.models.summary_analytics_model import SummaryAnalyticsRequest

class TimeSavedService:
    @staticmethod
    async def record_time_saved(
        client_id: str,
        clinician_id: str,
        status: str,
        session_id: str,
        document_version_id: Optional[int] = None,
        procedure_code: Optional[str] = None,
        teams: Optional[str] = None,
        teams_code: Optional[str] = None,
        tool_used: Optional[bool] = None
    ) -> tuple[TimeSaved, bool]:
        doc_id = document_version_id or 0
        async with get_analytics_db() as db:
            if status == "note":
                return await TimeSavedService._handle_note(db, client_id, clinician_id, session_id, doc_id, procedure_code, teams, teams_code, tool_used)
            elif status == "sign":
                return await TimeSavedService._handle_sign(db, client_id, clinician_id, session_id, doc_id, procedure_code, teams, teams_code, tool_used)
            elif status == "save":
                return await TimeSavedService._handle_save(db, client_id, clinician_id, session_id, doc_id, procedure_code, teams, teams_code, tool_used)
            else:
                raise HTTPException(status_code=400, detail="Invalid status, must be note, save, or sign")

    @staticmethod
    async def _handle_note(
        db,
        client_id: str,
        clinician_id: str,
        session_id: str,
        doc_id: int,
        procedure_code: Optional[str],
        teams: Optional[str],
        teams_code: Optional[str],
        tool_used: Optional[bool]
    ) -> tuple[TimeSaved, bool]:
        stmt = select(TimeSaved).where(
            TimeSaved.client_id == client_id,
            TimeSaved.clinician_id == clinician_id,
            TimeSaved.session_id == session_id
        ).limit(1)
        result = await db.execute(stmt)
        existing = result.scalar_one_or_none()
        if existing:
            record = existing
            record.document_version_id = doc_id
            if procedure_code is not None:
                record.procedure_code = procedure_code
            if teams is not None:
                record.teams = teams
            if teams_code is not None:
                record.teams_code = teams_code
            if tool_used is not None:
                record.tool_used = bool(tool_used)
            record.status = "initialized"
            await db.commit()
            await db.refresh(record)
            if teams_code and (record.division is None or record.program is None):
                await TimeSavedService._populate_org_fields(db, record, clinician_id, teams_code)
                await db.commit()
                await db.refresh(record)
            return record, True
        if doc_id:
            stmt2 = select(TimeSaved).where(
                TimeSaved.client_id == client_id,
                TimeSaved.clinician_id == clinician_id,
                TimeSaved.document_version_id == doc_id
            ).limit(1)
            result2 = await db.execute(stmt2)
            existing_by_doc = result2.scalar_one_or_none()
            if existing_by_doc:
                return existing_by_doc, True
        record = TimeSaved(
            client_id=client_id,
            clinician_id=clinician_id,
            document_version_id=doc_id,
            session_id=session_id,
            start_timestamp=datetime.utcnow(),
            end_timestamp=None,
            duration=None,
            procedure_code=procedure_code,
            teams=teams,
            teams_code=teams_code,
            tool_used=bool(tool_used),
            status="initialized"
        )
        db.add(record)
        await db.commit()
        await db.refresh(record)
        if teams_code:
            await TimeSavedService._populate_org_fields(db, record, clinician_id, teams_code)
            await db.commit()
            await db.refresh(record)
        return record, False

    @staticmethod
    async def _handle_sign(
        db,
        client_id: str,
        clinician_id: str,
        session_id: str,
        doc_id: int,
        procedure_code: Optional[str],
        teams: Optional[str],
        teams_code: Optional[str],
        tool_used: Optional[bool]
    ) -> tuple[TimeSaved, bool]:
        stmt = select(TimeSaved).where(
            TimeSaved.client_id == client_id,
            TimeSaved.clinician_id == clinician_id,
            TimeSaved.session_id == session_id
        ).order_by(TimeSaved.start_timestamp.desc()).limit(1)
        result = await db.execute(stmt)
        record = result.scalar_one_or_none()
        if not record and doc_id:
            stmt2 = select(TimeSaved).where(
                TimeSaved.client_id == client_id,
                TimeSaved.clinician_id == clinician_id,
                TimeSaved.document_version_id == doc_id
            ).order_by(TimeSaved.start_timestamp.desc()).limit(1)
            result2 = await db.execute(stmt2)
            record = result2.scalar_one_or_none()
        if not record:
            raise HTTPException(status_code=404, detail="No matching time_saved record found for SIGN")
        current_time = datetime.utcnow()
        record.end_timestamp = current_time
        if record.start_timestamp and record.start_timestamp.tzinfo:
            start_ts = record.start_timestamp.astimezone(timezone.utc).replace(tzinfo=None)
        else:
            start_ts = record.start_timestamp
        record.duration = int((current_time - start_ts).total_seconds() / 60)
        if doc_id and not record.document_version_id:
            record.document_version_id = doc_id
        if procedure_code is not None:
            record.procedure_code = procedure_code
        if teams is not None:
            record.teams = teams
        if teams_code is not None:
            record.teams_code = teams_code
        if tool_used is not None:
            record.tool_used = bool(tool_used)
        record.status = "signed"
        await db.commit()
        await db.refresh(record)
        return record, False

    @staticmethod
    async def _handle_save(
        db,
        client_id: str,
        clinician_id: str,
        session_id: str,
        doc_id: int,
        procedure_code: Optional[str],
        teams: Optional[str],
        teams_code: Optional[str],
        tool_used: Optional[bool]
    ) -> tuple[TimeSaved, bool]:
        stmt = select(TimeSaved).where(
            TimeSaved.client_id == client_id,
            TimeSaved.clinician_id == clinician_id,
            TimeSaved.session_id == session_id
        ).order_by(TimeSaved.start_timestamp.desc()).limit(1)
        result = await db.execute(stmt)
        record = result.scalar_one_or_none()
        if not record and doc_id:
            stmt2 = select(TimeSaved).where(
                TimeSaved.client_id == client_id,
                TimeSaved.clinician_id == clinician_id,
                TimeSaved.document_version_id == doc_id
            ).order_by(TimeSaved.start_timestamp.desc()).limit(1)
            result2 = await db.execute(stmt2)
            record = result2.scalar_one_or_none()
        if not record:
            raise HTTPException(status_code=404, detail="No matching time_saved record found for SAVE")
        if doc_id and not record.document_version_id:
            record.document_version_id = doc_id
        if teams is not None:
            record.teams = teams
        if teams_code is not None:
            record.teams_code = teams_code
        if tool_used is not None:
            record.tool_used = bool(tool_used)
        record.status = "saved"
        await db.commit()
        await db.refresh(record)

        # Call save_summary if tool_used is True
        if tool_used:
            try:
                summary_request = SummaryAnalyticsRequest(
                    user_id=clinician_id,
                    document_id=doc_id,
                    clinician_type=None,  # Not available in current context
                    procedure_code=procedure_code,
                    auto_completion_used=True,  # Assuming tool usage means auto completion was used
                    summary_finalized=True,  # Assuming save means summary is finalized
                    start_timestamp=record.start_timestamp,
                    action_type="save"
                )
                await save_summary(summary_request)
            except Exception as e:
                # Log the error but don't fail the main operation
                from app.core.logger import logger
                logger.error(f"Error calling save_summary: {str(e)}")

        return record, False

    @staticmethod
    async def _populate_org_fields(db, record: TimeSaved, clinician_id: str, teams_code: Optional[str]):
        params = {"eid": int(clinician_id)}
        query = """SELECT org_level_1 AS division, org_level_1_code AS division_code,
                    org_level_2 AS program, org_level_2_code AS program_code
                    FROM users WHERE employee_id = :eid"""
        if teams_code:
            query += " AND org_level_3_code = :teams_code"
            params["teams_code"] = teams_code
        stmt = text(query)
        result = await db.execute(stmt, params)
        row = result.first()
        if row:
            record.division = row.division
            record.division_code = row.division_code
            record.program = row.program
            record.program_code = row.program_code