from fastapi import APIRouter
from app.connections.analytics_handler import Analytics<PERSON><PERSON><PERSON>
from app.models.summary_analytics_model import SummaryAnalyticsRequest
from app.core.logger import logger
import datetime
from app.modules.services.session_service import SessionService
from app.modules.services.summary_db_service import SummaryDBService

router = APIRouter()

@router.post("/save-summary")
async def save_summary(request: SummaryAnalyticsRequest):
    logger.info(f"Received summary analytics request | user_id={request.user_id} | document_id={request.document_id}")

    session_service = SessionService()

    session_id=""

    session = session_service.get_session_by_document_id(request.document_id)
    if not session:
        logger.warning(f"Session not found | document_id={request.document_id}")
    else:
        session_id = session.id
        session_service.close_session(session.id)
    
    summary_data = {
        "user_id": request.user_id,
        "session_id": session_id,
        "clinician_type": request.clinician_type,
        "procedure_code": request.procedure_code,
        "auto_completion_used": request.auto_completion_used,
        "summary_finalized": request.summary_finalized,
        "action_type": request.action_type,
    }

    current_time = datetime.datetime.now(datetime.timezone.utc).isoformat()
    
    if summary_data["summary_finalized"] is not None and summary_data["summary_finalized"] is True:
        summary_data["summary_finalized_timestamp"] =  current_time
       
    summary_data["end_timestamp"] = current_time

    async with AnalyticsHandler() as analytics:
        api_result = await analytics.track_summary_analytics(summary_data)
    # Store in summary database
    end_time = datetime.datetime.fromisoformat(summary_data["end_timestamp"].replace('Z', '+00:00'))
    finalized_ts = None
    if summary_data.get("summary_finalized_timestamp"):
        finalized_ts = datetime.datetime.fromisoformat(summary_data["summary_finalized_timestamp"].replace('Z', '+00:00'))
    start_ts = request.start_timestamp
    time_consumed = int((end_time - start_ts).total_seconds()) if start_ts else None
    await SummaryDBService.store_summary_analytics(
        user_id=request.user_id,
        session_id=str(session_id),
        clinician_type=request.clinician_type,
        procedure_code=request.procedure_code,
        auto_completion_used=request.auto_completion_used,
        summary_finalized=request.summary_finalized,
        start_timestamp=start_ts,
        end_timestamp=end_time,
        summary_finalized_timestamp=finalized_ts,
        time_consumed_secs=time_consumed,
        summary=request.summary
    )
    return api_result
