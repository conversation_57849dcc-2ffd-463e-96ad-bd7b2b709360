from fastapi import APIRouter, Depends
from app.modules.routers.tools import router as tools_router
from app.modules.routers.feedback import router as feedback_router
from app.modules.routers.session import router as session_router
from app.modules.routers.time_saved import router as time_saved_router

from app.core.auth import verify_api_key
from app.core.config import API_PREFIX



router = APIRouter(prefix=API_PREFIX, tags=["tools"], dependencies=[Depends(verify_api_key)])
router.include_router(tools_router)
router.include_router(feedback_router)
router.include_router(session_router)
router.include_router(time_saved_router)


