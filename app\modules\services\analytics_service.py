import datetime

from app.core.config import ANALYTICS_API_BASE_URL, ANALYTICS_API_KEY, ANALYTICS_SUMMARY_ENDPOINT
from app.connections.analytics_handler import Analy<PERSON><PERSON>and<PERSON>
from app.core.logger import logger
from app.modules.services.summary_db_service import SummaryDBService

class SummaryAnalyticsService:
    
    
    def __init__(self):
        self.api_url = ANALYTICS_API_BASE_URL + ANALYTICS_SUMMARY_ENDPOINT
        self.api_key = ANALYTICS_API_KEY
        
    async def track_initial_action(self, 
                                  user_id: str,
                                  session_id: int,
                                  clinician_type: str,
                                  procedure_code: str,
                                  start_timestamp: str = None):
        
        logger.info(f"Tracking initial action | user_id={user_id} | session_id={session_id}")
        if not start_timestamp:
            start_timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()
            
        payload = {
            "user_id": user_id,
            "session_id": session_id,
            "clinician_type": clinician_type,
            "procedure_code": procedure_code,
            "start_timestamp": start_timestamp,
        }
        
        # Store in analytics API
        async with AnalyticsHandler() as analytics:
            api_result = await analytics.track_summary_analytics(payload)
        # Store in summary database
        start_timestamp_dt = datetime.datetime.fromisoformat(start_timestamp.replace('Z', '+00:00')) if isinstance(start_timestamp, str) else start_timestamp
        await SummaryDBService.store_summary_analytics(
            user_id=user_id,
            session_id=str(session_id),
            clinician_type=clinician_type,
            procedure_code=procedure_code,
            start_timestamp=start_timestamp_dt,
            auto_completion_used=False,
            summary_finalized=False
        )
        return api_result
    
    