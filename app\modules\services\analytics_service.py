import datetime

from app.core.config import ANALYTICS_API_BASE_URL, ANALYTICS_API_KEY, ANALYTICS_SUMMARY_ENDPOINT
from app.connections.analytics_handler import Analy<PERSON><PERSON>and<PERSON>
from app.core.logger import logger
from app.modules.services.summary_db_service import SummaryDBService
from app.models.summary_analytics_model import SummaryAnalyticsRequest
from app.modules.services.session_service import SessionService

class SummaryAnalyticsService:
    
    
    def __init__(self):
        self.api_url = ANALYTICS_API_BASE_URL + ANALYTICS_SUMMARY_ENDPOINT
        self.api_key = ANALYTICS_API_KEY
        
    async def track_initial_action(self, 
                                  client_id: str,
                                  session_id: int,
                                  clinician_type: str,
                                  procedure_code: str,
                                  start_timestamp: str = None):
        
        logger.info(f"Tracking initial action | user_id={client_id} | session_id={session_id}")
        if not start_timestamp:
            start_timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()
            
        payload = {
            "client_id": client_id,
            "session_id": session_id,
            "clinician_type": clinician_type,
            "procedure_code": procedure_code,
            "start_timestamp": start_timestamp,
        }
        
        # Store in analytics API
        async with AnalyticsHandler() as analytics:
            api_result = await analytics.track_summary_analytics(payload)
        # Store in summary database
        start_timestamp_dt = datetime.datetime.fromisoformat(start_timestamp.replace('Z', '+00:00')) if isinstance(start_timestamp, str) else start_timestamp
        await SummaryDBService.store_summary_analytics(
            user_id=client_id,
            session_id=str(session_id),
            clinician_type=clinician_type,
            procedure_code=procedure_code,
            start_timestamp=start_timestamp_dt,
            auto_completion_used=False,
            summary_finalized=False
        )
        return api_result

    async def save_summary(self, request: SummaryAnalyticsRequest):
        """
        Save summary analytics data.

        Args:
            request (SummaryAnalyticsRequest): The summary analytics request data

        Returns:
            dict: Result from analytics API
        """
        logger.info(f"Processing summary analytics request | user_id={request.user_id} | document_id={request.document_id}")

        session_service = SessionService()
        session_id = ""

        session = session_service.get_session_by_document_id(request.document_id)
        if not session:
            logger.warning(f"Session not found | document_id={request.document_id}")
        else:
            session_id = session.id
            session_service.close_session(session.id)

        summary_data = {
            "clinician_id": request.clinician_id,
            "session_id": session_id,
            "clinician_type": request.clinician_type,
            "procedure_code": request.procedure_code,
            "auto_completion_used": request.auto_completion_used,
            "summary_finalized": request.summary_finalized,
            "action_type": request.action_type,
        }

        current_time = datetime.datetime.now(datetime.timezone.utc).isoformat()

        if summary_data["summary_finalized"] is not None and summary_data["summary_finalized"] is True:
            summary_data["summary_finalized_timestamp"] = current_time

        summary_data["end_timestamp"] = current_time

        async with AnalyticsHandler() as analytics:
            api_result = await analytics.track_summary_analytics(summary_data)

        # Store in summary database
        end_time = datetime.datetime.fromisoformat(summary_data["end_timestamp"].replace('Z', '+00:00'))
        finalized_ts = None
        if summary_data.get("summary_finalized_timestamp"):
            finalized_ts = datetime.datetime.fromisoformat(summary_data["summary_finalized_timestamp"].replace('Z', '+00:00'))
        start_ts = request.start_timestamp
        time_consumed = int((end_time - start_ts).total_seconds()) if start_ts else None

        await SummaryDBService.store_summary_analytics(
            user_id=request.user_id,
            session_id=str(session_id),
            clinician_type=request.clinician_type,
            procedure_code=request.procedure_code,
            auto_completion_used=request.auto_completion_used,
            summary_finalized=request.summary_finalized,
            start_timestamp=start_ts,
            end_timestamp=end_time,
            summary_finalized_timestamp=finalized_ts,
            time_consumed_secs=time_consumed,
            summary=request.summary
        )

        return api_result
